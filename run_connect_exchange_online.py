import argparse
import os
import shutil
import subprocess
import sys
from typing import Optional


def find_powershell(executable_preference: Optional[str] = None) -> Optional[str]:
    """
    Find a PowerShell executable on Windows.
    Preference order:
      1) explicit path provided by user
      2) powershell.exe (Windows PowerShell)
      3) pwsh.exe (PowerShell 7+)
    """
    candidates = []

    if executable_preference:
        candidates.append(executable_preference)

    # Windows PowerShell
    candidates.append("powershell.exe")
    # PowerShell 7+
    candidates.append("pwsh.exe")

    for exe in candidates:
        if os.path.isabs(exe) and os.path.isfile(exe):
            return exe
        # Try to resolve on PATH
        resolved = shutil.which(exe)
        if resolved:
            return resolved

    return None


def run_powershell_script(ps_exe: str, script_path: str, cwd: str) -> int:
    """
    Launch the given PowerShell script with sane defaults and stream output.
    Returns the process exit code.
    """
    # Use Bypass for execution policy so the script can run even if policy is restricted.
    cmd = [
        ps_exe,
        "-No<PERSON>rofile",
        "-ExecutionPolicy", "Bypass",
        "-File", script_path,
    ]

    print(f"Launching PowerShell: {' '.join(cmd)}")
    print(f"Working directory: {cwd}")
    print("")

    # Stream stdout/stderr live to this console
    proc = subprocess.Popen(
        cmd,
        cwd=cwd,
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        text=True,
        encoding="utf-8",
        errors="replace",
    )

    assert proc.stdout is not None
    for line in proc.stdout:
        # Print PowerShell output as-is
        print(line, end="")

    proc.wait()
    return proc.returncode


def validate_required_files(base_dir: str) -> None:
    """
    Validate that the supporting files referenced by the PowerShell script are present.
    The script expects these files to be next to Connect-ExchangeOnline.ps1 by default.
    """
    missing = []
    for fname in ("Connect-ExchangeOnline.ps1", "netadmin_Credential.xml", "users_to_migrated.csv"):
        path = os.path.join(base_dir, fname)
        if not os.path.isfile(path):
            missing.append(path)

    if missing:
        msg = (
            "Missing required file(s):\n  - "
            + "\n  - ".join(missing)
            + "\nPlease ensure these files exist in the same directory as the Python script or provide the correct paths."
        )
        raise FileNotFoundError(msg)


def main(argv: Optional[list] = None) -> int:
    parser = argparse.ArgumentParser(
        description=(
            "Run the existing Connect-ExchangeOnline.ps1 PowerShell script from Python, "
            "streaming output and returning its exit code."
        )
    )
    parser.add_argument(
        "--ps",
        dest="ps_path",
        default=None,
        help="Optional path to PowerShell executable (powershell.exe or pwsh.exe). Defaults to auto-detect.",
    )
    parser.add_argument(
        "--script",
        dest="script_path",
        default=None,
        help=(
            "Optional path to Connect-ExchangeOnline.ps1. "
            "Defaults to the file named 'Connect-ExchangeOnline.ps1' in this directory."
        ),
    )

    args = parser.parse_args(argv)

    # Determine base directory (where this Python script resides)
    base_dir = os.path.abspath(os.path.dirname(__file__))

    # Resolve script path
    script_path = args.script_path or os.path.join(base_dir, "Connect-ExchangeOnline.ps1")
    script_path = os.path.abspath(script_path)

    if not os.path.isfile(script_path):
        print(f"ERROR: Could not find PowerShell script at: {script_path}", file=sys.stderr)
        return 2

    # Validate required files exist next to the PS1 (as referenced by that script)
    ps1_dir = os.path.dirname(script_path)
    try:
        validate_required_files(ps1_dir)
    except FileNotFoundError as e:
        print(str(e), file=sys.stderr)
        return 3

    # Ensure we're on Windows; otherwise powershell.exe may not exist
    if os.name != "nt":
        print(
            "WARNING: This script is intended to run on Windows. Attempting to locate 'pwsh.exe' anyway...",
            file=sys.stderr,
        )

    # Find PowerShell
    ps_exe = find_powershell(args.ps_path)
    if not ps_exe:
        print(
            "ERROR: Could not find PowerShell executable (powershell.exe or pwsh.exe).\n"
            "- If you have PowerShell 7 installed, ensure 'pwsh.exe' is on PATH.\n"
            "- Otherwise, ensure 'powershell.exe' is available (Windows PowerShell).",
            file=sys.stderr,
        )
        return 4

    # Run the script
    exit_code = run_powershell_script(ps_exe, script_path, cwd=ps1_dir)

    if exit_code == 0:
        print("\nPython wrapper: PowerShell script completed successfully.")
    else:
        print(f"\nPython wrapper: PowerShell script exited with code {exit_code}.", file=sys.stderr)

    return exit_code


if __name__ == "__main__":
    raise SystemExit(main())

