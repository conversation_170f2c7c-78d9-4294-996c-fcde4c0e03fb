import argparse
import os
import shutil
import subprocess
import sys
from typing import Optional


def find_powershell(executable_preference: Optional[str] = None) -> Optional[str]:
    """
    Find pwsh.exe (PowerShell 7+) only. Returns full path if found.
    Preference:
      1) explicit path provided by user
      2) pwsh.exe discovered on PATH
    """
    candidates = []

    if executable_preference:
        candidates.append(executable_preference)

    # PowerShell 7+
    candidates.append("pwsh.exe")

    for exe in candidates:
        if os.path.isabs(exe) and os.path.isfile(exe):
            return exe
        # Try to resolve on PATH
        resolved = shutil.which(exe)
        if resolved:
            return resolved

    return None


def get_pwsh_version(ps_exe: str) -> Optional[str]:
    """
    Return the pwsh.exe version string (e.g., '7.5.2'), or None if it cannot be determined.
    """
    try:
        out = subprocess.check_output(
            [ps_exe, "-NoProfile", "-NoLogo", "-Command", "$PSVersionTable.PSVersion.ToString()"],
            text=True,
            stderr=subprocess.STDOUT,
        )
        ver = out.strip().splitlines()[-1].strip()
        return ver or None
    except Exception:
        return None


def run_powershell_inline(ps_exe: str, script_text: str, cwd: str) -> int:
    """
    Launch pwsh.exe with a supplied PowerShell script (inline) using -EncodedCommand,
    stream output, and return the exit code.
    """
    # Encode the script as Base64-encoded UTF-16LE (what PowerShell expects)
    encoded = script_text.encode("utf-16le")
    import base64
    b64 = base64.b64encode(encoded).decode("ascii")

    cmd = [
        ps_exe,
        "-NoProfile",
        "-NonInteractive",
        "-ExecutionPolicy", "Bypass",
        "-EncodedCommand", b64,
    ]

    print(f"Launching pwsh inline (-EncodedCommand). Working directory: {cwd}")

    proc = subprocess.Popen(
        cmd,
        cwd=cwd,
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        text=True,
        encoding="utf-8",
        errors="replace",
    )

    assert proc.stdout is not None
    for line in proc.stdout:
        print(line, end="")

    proc.wait()
    return proc.returncode


def validate_required_files(base_dir: str) -> None:
    """
    Validate that the supporting files are present next to this Python script.
    Required: netadmin_Credential.xml and users_to_migrated.csv
    """
    missing = []
    for fname in ("netadmin_Credential.xml", "users_to_migrated.csv"):
        path = os.path.join(base_dir, fname)
        if not os.path.isfile(path):
            missing.append(path)

    if missing:
        msg = (
            "Missing required file(s):\n  - "
            + "\n  - ".join(missing)
            + "\nPlease ensure these files exist in the same directory as this Python script."
        )
        raise FileNotFoundError(msg)


def main(argv: Optional[list] = None) -> int:
    parser = argparse.ArgumentParser(
        description=(
            "Run the existing Connect-ExchangeOnline.ps1 PowerShell script from Python, "
            "streaming output and returning its exit code."
        )
    )
    parser.add_argument(
        "--ps",
        dest="ps_path",
        default=None,
        help="Optional full path to pwsh.exe (PowerShell 7). Defaults to auto-detect on PATH.",
    )

    args = parser.parse_args(argv)

    # Determine base directory (where this Python script resides)
    base_dir = os.path.abspath(os.path.dirname(__file__))

    # Validate required supporting files exist next to this Python script
    try:
        validate_required_files(base_dir)
    except FileNotFoundError as e:
        print(str(e), file=sys.stderr)
        return 3

    # Ensure we're on Windows; otherwise powershell.exe may not exist
    if os.name != "nt":
        print(
            "WARNING: This script is intended to run on Windows. Attempting to locate 'pwsh.exe' anyway...",
            file=sys.stderr,
        )

    # Find PowerShell 7 (pwsh.exe) only
    ps_exe = find_powershell(args.ps_path)
    if not ps_exe:
        print(
            "ERROR: Could not find 'pwsh.exe' (PowerShell 7). Ensure it is installed and on PATH, "
            "or provide its full path via --ps.",
            file=sys.stderr,
        )
        return 4

    # Verify required version
    expected_version = "7.5.2"
    ver = get_pwsh_version(ps_exe)
    if not ver:
        print(
            "ERROR: Unable to determine pwsh.exe version. Ensure PowerShell 7 is installed and accessible.",
            file=sys.stderr,
        )
        return 5
    if ver != expected_version:
        print(
            f"ERROR: pwsh.exe version mismatch. Expected {expected_version}, found {ver}.",
            file=sys.stderr,
        )
        print("Please install PowerShell 7.5.2 and try again.", file=sys.stderr)
        return 6

    # Build the inline PowerShell script (no external .ps1 file required)
    ps_script = r"""
$ErrorActionPreference = 'Stop'

# Use current working directory as the script directory
$ScriptDirectory = (Get-Location).Path
$CredentialPath = Join-Path -Path $ScriptDirectory -ChildPath 'netadmin_Credential.xml'
$CsvPath = Join-Path -Path $ScriptDirectory -ChildPath 'users_to_migrated.csv'

Write-Host 'Exchange Online Connection (inline from Python)' -ForegroundColor Green
Write-Host '================================================' -ForegroundColor Green
Write-Host ''

try {
    if (-not (Test-Path -Path $CredentialPath)) {
        throw "Credential file not found at: $CredentialPath"
    }

    Write-Host "Loading credentials from: $CredentialPath" -ForegroundColor Yellow
    $credential = Import-CliXml -Path $CredentialPath
    if ($null -eq $credential) {
        throw 'Failed to import credentials from XML file'
    }
    Write-Host "Credentials loaded for user: $($credential.UserName)" -ForegroundColor Green

    Write-Host 'Checking for Exchange Online Management module...' -ForegroundColor Yellow
    if (-not (Get-Module -ListAvailable -Name ExchangeOnlineManagement)) {
        Write-Host 'Exchange Online Management module not found. Installing...' -ForegroundColor Yellow
        try {
            Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser -Force
            Install-Module -Name ExchangeOnlineManagement -Force -AllowClobber -Scope CurrentUser
            Write-Host 'Exchange Online Management module installed successfully.' -ForegroundColor Green
        }
        catch {
            Write-Host 'Failed to install Exchange Online Management module automatically.' -ForegroundColor Red
            Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
            throw 'Failed to install ExchangeOnlineManagement module'
        }
    } else {
        Write-Host 'Exchange Online Management module is already installed.' -ForegroundColor Green
    }

    Import-Module ExchangeOnlineManagement -Force
    Write-Host 'Exchange Online Management module loaded successfully.' -ForegroundColor Green

    Write-Host 'Connecting to Exchange Online...' -ForegroundColor Yellow
    Connect-ExchangeOnline -Credential $credential -ShowProgress $true

    Start-Sleep -Seconds 3

    Write-Host 'Verifying connection...' -ForegroundColor Yellow
    $orgInfo = Get-OrganizationConfig | Select-Object Name, Identity
    if (-not $orgInfo) {
        throw 'Connection verification failed'
    }
    Write-Host "SUCCESS: Connected to Exchange Online: $($orgInfo.Name) ($($orgInfo.Identity))" -ForegroundColor Green

    if (-not (Test-Path -Path $CsvPath)) {
        throw "CSV file not found at: $CsvPath"
    }
    Write-Host "Found CSV file: $CsvPath" -ForegroundColor Green

    $RandomString = -join ((65..90) + (97..122) + (48..57) | Get-Random -Count 8 | ForEach-Object {[char]$_})
    $MigrationBatchName = "Migrate-$RandomString"
    Write-Host "Generated migration batch name: $MigrationBatchName" -ForegroundColor Yellow

    Write-Host 'Reading CSV file data...' -ForegroundColor Yellow
    $csvData = [System.IO.File]::ReadAllBytes($CsvPath)
    if ($csvData.Length -eq 0) { throw 'CSV file is empty or could not be read' }
    Write-Host "CSV file read successfully. Size: $($csvData.Length) bytes" -ForegroundColor Green

    Write-Host 'Creating migration batch...' -ForegroundColor Yellow
    New-MigrationBatch -Name $MigrationBatchName `
        -SourceEndpoint 'mail.ais-kuwait.org' `
        -CSVData $csvData `
        -TargetDeliveryDomain 'aiskuwait.mail.onmicrosoft.com' `
        -AutoStart

    Write-Host "SUCCESS: Migration batch '$MigrationBatchName' created and started." -ForegroundColor Green
}
catch {
    Write-Host ''
    Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ''
    Write-Host 'Please check:' -ForegroundColor Yellow
    Write-Host '1. The credential file exists and is valid' -ForegroundColor Yellow
    Write-Host '2. The stored credentials are correct' -ForegroundColor Yellow
    Write-Host '3. Your internet connection is working' -ForegroundColor Yellow
    Write-Host '4. Exchange Online Management module is installed' -ForegroundColor Yellow
    Write-Host "5. The CSV file 'users_to_migrated.csv' exists in the script directory" -ForegroundColor Yellow
    Write-Host '6. You have permissions to create migration batches' -ForegroundColor Yellow
    exit 1
}

Write-Host ''
Write-Host 'Script completed successfully!' -ForegroundColor Green
Write-Host 'Exchange Online session will remain active until you close PowerShell or run Disconnect-ExchangeOnline' -ForegroundColor Cyan
exit 0
"""

    # Run the inline PowerShell script, using this Python script's directory as working dir
    exit_code = run_powershell_inline(ps_exe, ps_script, cwd=base_dir)

    if exit_code == 0:
        print("\nPython wrapper: PowerShell script completed successfully.")
    else:
        print(f"\nPython wrapper: PowerShell script exited with code {exit_code}.", file=sys.stderr)

    return exit_code


if __name__ == "__main__":
    raise SystemExit(main())

