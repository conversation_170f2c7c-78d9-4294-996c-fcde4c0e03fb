import argparse
import os
import shutil
import subprocess
import sys
from typing import Optional


def find_powershell(executable_preference: Optional[str] = None) -> Optional[str]:
    """
    Find pwsh.exe (PowerShell 7+) only. Returns full path if found.
    Preference:
      1) explicit path provided by user
      2) pwsh.exe discovered on PATH
    """
    candidates = []

    if executable_preference:
        candidates.append(executable_preference)

    # PowerShell 7+
    candidates.append("pwsh.exe")

    for exe in candidates:
        if os.path.isabs(exe) and os.path.isfile(exe):
            return exe
        # Try to resolve on PATH
        resolved = shutil.which(exe)
        if resolved:
            return resolved

    return None


def get_pwsh_version(ps_exe: str) -> Optional[str]:
    """
    Return the pwsh.exe version string (e.g., '7.5.2'), or None if it cannot be determined.
    """
    try:
        out = subprocess.check_output(
            [ps_exe, "-NoProfile", "-NoLogo", "-Command", "$PSVersionTable.PSVersion.ToString()"],
            text=True,
            stderr=subprocess.STDOUT,
        )
        ver = out.strip().splitlines()[-1].strip()
        return ver or None
    except Exception:
        return None


def run_powershell_script(ps_exe: str, script_path: str, cwd: str) -> int:
    """
    Launch the given PowerShell script with sane defaults and stream output.
    Returns the process exit code.
    """
    # Use Bypass for execution policy so the script can run even if policy is restricted.
    cmd = [
        ps_exe,
        "-NoProfile",
        "-ExecutionPolicy", "Bypass",
        "-File", script_path,
    ]

    print(f"Launching PowerShell: {' '.join(cmd)}")
    print(f"Working directory: {cwd}")
    print("")

    # Stream stdout/stderr live to this console
    proc = subprocess.Popen(
        cmd,
        cwd=cwd,
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        text=True,
        encoding="utf-8",
        errors="replace",
    )

    assert proc.stdout is not None
    for line in proc.stdout:
        # Print PowerShell output as-is
        print(line, end="")

    proc.wait()
    return proc.returncode


def validate_required_files(base_dir: str) -> None:
    """
    Validate that the supporting files referenced by the PowerShell script are present.
    The script expects these files to be next to Connect-ExchangeOnline.ps1 by default.
    """
    missing = []
    for fname in ("Connect-ExchangeOnline.ps1", "netadmin_Credential.xml", "users_to_migrated.csv"):
        path = os.path.join(base_dir, fname)
        if not os.path.isfile(path):
            missing.append(path)

    if missing:
        msg = (
            "Missing required file(s):\n  - "
            + "\n  - ".join(missing)
            + "\nPlease ensure these files exist in the same directory as the Python script or provide the correct paths."
        )
        raise FileNotFoundError(msg)


def main(argv: Optional[list] = None) -> int:
    parser = argparse.ArgumentParser(
        description=(
            "Run the existing Connect-ExchangeOnline.ps1 PowerShell script from Python, "
            "streaming output and returning its exit code."
        )
    )
    parser.add_argument(
        "--ps",
        dest="ps_path",
        default=None,
        help="Optional full path to pwsh.exe (PowerShell 7). Defaults to auto-detect on PATH.",
    )
    parser.add_argument(
        "--script",
        dest="script_path",
        default=None,
        help=(
            "Optional path to Connect-ExchangeOnline.ps1. "
            "Defaults to the file named 'Connect-ExchangeOnline.ps1' in this directory."
        ),
    )

    args = parser.parse_args(argv)

    # Determine base directory (where this Python script resides)
    base_dir = os.path.abspath(os.path.dirname(__file__))

    # Resolve script path
    script_path = args.script_path or os.path.join(base_dir, "Connect-ExchangeOnline.ps1")
    script_path = os.path.abspath(script_path)

    if not os.path.isfile(script_path):
        print(f"ERROR: Could not find PowerShell script at: {script_path}", file=sys.stderr)
        return 2

    # Validate required files exist next to the PS1 (as referenced by that script)
    ps1_dir = os.path.dirname(script_path)
    try:
        validate_required_files(ps1_dir)
    except FileNotFoundError as e:
        print(str(e), file=sys.stderr)
        return 3

    # Ensure we're on Windows; otherwise powershell.exe may not exist
    if os.name != "nt":
        print(
            "WARNING: This script is intended to run on Windows. Attempting to locate 'pwsh.exe' anyway...",
            file=sys.stderr,
        )

    # Find PowerShell 7 (pwsh.exe) only
    ps_exe = find_powershell(args.ps_path)
    if not ps_exe:
        print(
            "ERROR: Could not find 'pwsh.exe' (PowerShell 7). Ensure it is installed and on PATH, "
            "or provide its full path via --ps.",
            file=sys.stderr,
        )
        return 4

    # Verify required version
    expected_version = "7.5.2"
    ver = get_pwsh_version(ps_exe)
    if not ver:
        print(
            "ERROR: Unable to determine pwsh.exe version. Ensure PowerShell 7 is installed and accessible.",
            file=sys.stderr,
        )
        return 5
    if ver != expected_version:
        print(
            f"ERROR: pwsh.exe version mismatch. Expected {expected_version}, found {ver}.",
            file=sys.stderr,
        )
        print("Please install PowerShell 7.5.2 and try again.", file=sys.stderr)
        return 6

    # Run the script
    exit_code = run_powershell_script(ps_exe, script_path, cwd=ps1_dir)

    if exit_code == 0:
        print("\nPython wrapper: PowerShell script completed successfully.")
    else:
        print(f"\nPython wrapper: PowerShell script exited with code {exit_code}.", file=sys.stderr)

    return exit_code


if __name__ == "__main__":
    raise SystemExit(main())

