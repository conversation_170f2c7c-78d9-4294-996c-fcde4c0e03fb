# PowerShell Script to Connect to Exchange Online using Stored Credentials
# Author: Auto-generated script
# Description: Imports credentials from XML file and establishes Exchange Online connection

# Set error action preference to stop on errors
$ErrorActionPreference = "Stop"

# Get the script directory to ensure we're looking in the right place for the credential file
$ScriptDirectory = Split-Path -Parent $MyInvocation.MyCommand.Definition
$CredentialPath = Join-Path -Path $ScriptDirectory -ChildPath "netadmin_Credential.xml"

Write-Host "Exchange Online Connection Script" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green
Write-Host ""

try {
    # Check if the credential file exists
    if (-not (Test-Path -Path $CredentialPath)) {
        throw "Credential file not found at: $CredentialPath"
    }
    
    Write-Host "Loading credentials from: $CredentialPath" -ForegroundColor Yellow
    
    # Import the credential from the XML file
    $credential = Import-CliXml -Path $CredentialPath
    
    if ($null -eq $credential) {
        throw "Failed to import credentials from XML file"
    }
    
    Write-Host "Credentials loaded successfully for user: $($credential.UserName)" -ForegroundColor Green
    Write-Host ""
    
    # Check if Exchange Online Management module is available
    Write-Host "Checking for Exchange Online Management module..." -ForegroundColor Yellow

    if (-not (Get-Module -ListAvailable -Name ExchangeOnlineManagement)) {
        Write-Host "Exchange Online Management module not found!" -ForegroundColor Yellow
        Write-Host "Installing Exchange Online Management module automatically..." -ForegroundColor Yellow

        try {
            # Set execution policy temporarily if needed and install the module
            Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser -Force
            Install-Module -Name ExchangeOnlineManagement -Force -AllowClobber -Scope CurrentUser
            Write-Host "Exchange Online Management module installed successfully!" -ForegroundColor Green
        }
        catch {
            Write-Host "Failed to install Exchange Online Management module automatically." -ForegroundColor Red
            Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
            Write-Host "Please try running PowerShell as Administrator and run the script again." -ForegroundColor Yellow
            throw "Failed to install ExchangeOnlineManagement module"
        }
    } else {
        Write-Host "Exchange Online Management module is already installed" -ForegroundColor Green
    }

    # Import the Exchange Online Management module
    Import-Module ExchangeOnlineManagement -Force
    Write-Host "Exchange Online Management module loaded successfully" -ForegroundColor Green
    Write-Host ""
    
    # Connect to Exchange Online
    Write-Host "Connecting to Exchange Online..." -ForegroundColor Yellow
    Write-Host "This may take a few moments..." -ForegroundColor Yellow
    
    Connect-ExchangeOnline -Credential $credential -ShowProgress $true
    
    # Wait a moment for the connection to fully establish
    Start-Sleep -Seconds 3
    
    # Verify the connection by getting organization information
    Write-Host ""
    Write-Host "Verifying connection..." -ForegroundColor Yellow
    
    $orgInfo = Get-OrganizationConfig | Select-Object Name, Identity
    
    if ($orgInfo) {
        Write-Host ""
        Write-Host "SUCCESS: Connected to Exchange Online!" -ForegroundColor Green
        Write-Host "Organization: $($orgInfo.Name)" -ForegroundColor Green
        Write-Host "Identity: $($orgInfo.Identity)" -ForegroundColor Green
        Write-Host ""
        Write-Host "Exchange Online session is now active and ready for use." -ForegroundColor Green
        Write-Host "You can now run Exchange Online PowerShell commands." -ForegroundColor Cyan
    } else {
        throw "Connection verification failed"
    }

    # Now create the migration batch
    Write-Host ""
    Write-Host "Creating Migration Batch..." -ForegroundColor Yellow
    Write-Host "===========================" -ForegroundColor Yellow
    Write-Host ""

    # Check if the CSV file exists
    $CsvPath = Join-Path -Path $ScriptDirectory -ChildPath "users_to_migrated.csv"

    if (-not (Test-Path -Path $CsvPath)) {
        throw "CSV file not found at: $CsvPath"
    }

    Write-Host "Found CSV file: $CsvPath" -ForegroundColor Green

    # Generate a random string for the migration batch name
    $RandomString = -join ((65..90) + (97..122) + (48..57) | Get-Random -Count 8 | ForEach-Object {[char]$_})
    $MigrationBatchName = "Migrate-$RandomString"

    Write-Host "Generated migration batch name: $MigrationBatchName" -ForegroundColor Yellow
    Write-Host ""

    # Read the CSV file as bytes
    Write-Host "Reading CSV file data..." -ForegroundColor Yellow
    $csvData = [System.IO.File]::ReadAllBytes($CsvPath)

    if ($csvData.Length -eq 0) {
        throw "CSV file is empty or could not be read"
    }

    Write-Host "CSV file read successfully. Size: $($csvData.Length) bytes" -ForegroundColor Green
    Write-Host ""

    # Create the migration batch
    Write-Host "Creating migration batch..." -ForegroundColor Yellow
    Write-Host "This may take a few moments..." -ForegroundColor Yellow
    Write-Host ""

    New-MigrationBatch -Name $MigrationBatchName `
        -SourceEndpoint "mail.ais-kuwait.org" `
        -CSVData $csvData `
        -TargetDeliveryDomain "aiskuwait.mail.onmicrosoft.com" `
        -AutoStart

    Write-Host ""
    Write-Host "SUCCESS: Migration batch '$MigrationBatchName' created successfully!" -ForegroundColor Green
    Write-Host "The migration batch has been started automatically." -ForegroundColor Green
    Write-Host ""
    Write-Host "Migration batch details:" -ForegroundColor Cyan
    Write-Host "- Name: $MigrationBatchName" -ForegroundColor Cyan
    Write-Host "- Source Endpoint: mail.ais-kuwait.org" -ForegroundColor Cyan
    Write-Host "- Target Domain: aiskuwait.mail.onmicrosoft.com" -ForegroundColor Cyan
    Write-Host "- CSV File: users_to_migrated.csv" -ForegroundColor Cyan
    Write-Host "- Auto-started: Yes" -ForegroundColor Cyan

} catch {
    Write-Host ""
    Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Please check:" -ForegroundColor Yellow
    Write-Host "1. The credential file exists and is valid" -ForegroundColor Yellow
    Write-Host "2. The stored credentials are correct" -ForegroundColor Yellow
    Write-Host "3. Your internet connection is working" -ForegroundColor Yellow
    Write-Host "4. Exchange Online Management module is installed" -ForegroundColor Yellow
    Write-Host "5. The CSV file 'users_to_migrated.csv' exists in the script directory" -ForegroundColor Yellow
    Write-Host "6. You have permissions to create migration batches" -ForegroundColor Yellow

    # Exit with error code
    exit 1
}

Write-Host ""
Write-Host "Script completed successfully!" -ForegroundColor Green
Write-Host "Exchange Online session will remain active until you close PowerShell or run Disconnect-ExchangeOnline" -ForegroundColor Cyan
